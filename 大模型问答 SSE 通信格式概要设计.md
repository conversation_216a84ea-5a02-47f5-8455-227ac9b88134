## 1. 引言
### 1.1. 目的
本文档旨在定义一套标准化的数据格式和通信规范，用于大模型后端通过 Server-Sent Events (SSE) 向前端推送包含推理过程和最终结果的问答内容。前端可以根据规范中定义的标签 (Tag) 解析数据并进行相应的展示。

### 1.2. 背景
为了提升用户体验，让用户了解大模型生成答案的思考过程，需要在传统的问答结果基础上，增加展示模型的推理步骤。使用 SSE 技术可以实现服务器向客户端的实时推送，适合逐步展示推理过程和最终答案。

### 1.3. 范围
本文档定义了 SSE 通信中的事件类型 (Tag)、数据负载 (Payload) 的 JSON 结构以及交互流程。不涉及具体的前后端代码实现细节。

## 2. 设计目标
+ **结构清晰:** 回答内容需明确划分为推理部分和回答部分。
+ **过程透明:** 推理部分需能展示详细的步骤，包括步骤名称、执行动作和结果。
+ **引用明确:** 最终回答能关联到推理过程中引用的数据来源。
+ **实时流畅:** 利用 SSE 实现内容的逐步推送，避免长时间等待。
+ **易于解析:** 前端能通过统一的 Tag 和数据结构轻松解析并渲染。

## 3. 技术选型
+ **通信协议:** Server-Sent Events (SSE)
    - 优点：基于 HTTP，实现简单，支持服务器单向推送，适合流式数据传输。
+ **数据格式:** JSON
    - 优点：轻量级，易于人类阅读和机器解析，广泛支持。

## 4. 通信协议 (基于 SSE)
后端通过 `SseEmitter` 向前端推送事件流。每个事件包含 `event` (作为 Tag) 和 `data` (JSON 字符串) 两个关键部分。

### 4.1. 事件类型 (Tags)
定义以下事件类型 (Tags) 来标识不同的数据内容：

| Tag (`event`) | 描述 | 对应数据结构 (见 5.1 & 5.2) |
| :--- | :--- | :--- |
| `connected` | 标志 SSE 连接已成功建立 | `ConnectionInfo` |
| `inference_start` | 标志推理过程开始 | `null` 或空对象 `{}` |
| `inference_step_start` | 标志一个推理步骤的开始 | `InferenceStepStart` |
| `inference_step_update` | 更新推理步骤的内容，支持追加和换行模式 | `InferenceStepUpdate` |
| `inference_step_end` | 标志一个推理步骤的结束 | `InferenceStepEnd` |
| `inference_end` | 标志推理过程结束 | `null` 或空对象 `{}` |
| `answer_start` | 标志最终回答内容开始推送 | `null` 或空对象 `{}` |
| `answer_chunk` | 推送最终回答内容的一个片段 | `AnswerChunk` |
| `answer_references` | 推送最终回答引用的数据来源索引 | `AnswerReferences` |
| `answer_more_link` | 一些特殊情况，比如前段需要更多数据或更多列表页跳转时需要该标签显示后续请求内容 | `AnswerMoreLink` |
| `answer_end` | 标志最终回答内容推送结束 | `null` 或空对象 `{}` |
| answer_step_start | 回答中每个步骤的开始标签 |  |
| answer_step_update | 对于当前标签步骤的额外描述 |  |
| answer_step_end | 回答中每个步骤的结束标签 |  |
| `error` | 推送处理过程中发生的错误信息 | `ErrorPayload` |
| `stream_end` | 标志整个 SSE 流结束 | `null` 或空对象 `{}` |


_注：新版实现采用了步骤生命周期方法（_`inference_step_start`_, _`inference_step_update`_, _`inference_step_end`_），提供更精细的步骤控制。_

### 4.2. 数据格式 (JSON)
所有 `data` 字段的值都应是 JSON 格式的字符串。

## 5. 数据结构详解
### 5.1. 推理部分数据结构
![](https://cdn.nlark.com/yuque/0/2025/png/12785224/1745479846050-4a5a545d-d859-4a24-a3fc-bb85f47fabcc.png)

+ `ConnectionInfo`** (对应 **`connected`** Tag)**

```json
{
  "message": "string",   // 连接状态信息
  "requestId": "string"  // 请求的唯一标识符
}
```

+ `InferenceStepStart`** (对应 **`inference_step_start`** Tag)**

```json
{
  "stepId": "string",      // 步骤的唯一标识符
  "stepName": "string",    // 步骤的名称
  "status": "running"      // 步骤的初始状态
}
```

+ `InferenceStepUpdate`** (对应 **`inference_step_update`** Tag)**

```json
{
  "stepId": "string",       // 对应步骤的唯一标识符
  "updateContent": "string", // 步骤执行过程中的更新信息
  "mode": "append" | "newline" // 更新模式：追加或换行追加
}
```

```json
{
  "stepId": "toolcall",       // 对应步骤的唯一标识符
  "updateContent": [{
      "name": "",             // 调用名称
      "arguments": ""         // 调用参数
    }], // toolcall结果
  "mode": "newline" // 换行追加
}
```



+ `InferenceStepEnd`** (对应 **`inference_step_end`** Tag)**

```json
{
  "stepId": "string",       // 步骤的唯一标识符
  "stepName": "string",     // 步骤的名称
  "result": "string",       // 步骤执行的结果简述
  "status": "completed" | "failed", // 步骤最终状态
  "referenceIds": ["string"] | null // (可选) 此步骤关联的数据引用 ID 列表
}
```

### 5.2. 回答部分数据结构
+ `AnswerChunk`** (对应 **`answer_chunk`** Tag)**

```json
{
  "text": "string",  // 回答内容的文本片段
  "index": number    // (可选) 片段的顺序索引，用于前端拼接
}
```

+ `AnswerReferences`** (对应 **`answer_references`** Tag)**

```json
{
  "referenceIds": ["string"] // 最终回答内容引用的推理步骤中的 `referenceIds` 或 `stepId` 列表
}
```

+ **<font style="background-color:#EFF0F0;"> AnswerMoreLink </font>****（对应****<font style="background-color:#EFF0F0;"> answer_more_link </font>****Tag）**

```java
{
  "url": "string",    // 获取更多数据的URL地址
  "method": "string", // 请求方法类型：GET, POST, PUT等
  "requestBody": {    // (可选) POST/PUT等请求的请求体
    "key": "value"    // 请求体中的参数
  },
  "headers": {        // (可选) 请求头信息
    "key": "string"   // 请求头参数，如Content-Type, Authorization等
  },
  "metadata": {       // (可选) 附加元数据
    "totalCount": number,    // 结果总数量
    "currentPage": number,   // 当前页码
    "pageSize": number,      // 每页大小
    "displayedCount": number, // 已显示的数量
    "remainingCount": number, // 剩余数量
    "queryId": "string",     // 查询标识符
    "version": "string"      // 数据版本标识
  }
}

//举例
{
  "url": "https://example.com/api/search",
  "method": "POST",
  "requestBody": {
    "queryId": "search-123456",
    "page": 2,
    "pageSize": 10,
    "filters": {"category": "document"}
  },
  "headers": {
    "Content-Type": "application/json",
    "Authorization": "Bearer ${token}"
  },
  "metadata": {
    "totalCount": 1500,
    "currentPage": 1,
    "remainingCount": 1490
  }
}
```



<以下标签 可选非必须>

+ `AnswerStepStart`** (对应 **`answer_step_start`** Tag)**

```json
{
  "stepId": "string",      // 步骤的唯一标识符
  "stepName": "string",    // 步骤的名称
  "status": "running"      // 步骤的初始状态
}
```

+ `AnswerStepUpdate`** (对应 **`answer_step_update`** Tag)**

```json
{
  "stepId": "string",       // 对应步骤的唯一标识符
  "updateContent": "string", // 步骤执行过程中的更新信息
  "mode": "append" | "newline" // 更新模式：追加或换行追加
}
```



+ `AnswerStepEnd`** (对应 **`**answer_step_end**`** Tag)**

```json
{
  "stepId": "string",       // 步骤的唯一标识符
  "stepName": "string",     // 步骤的名称
  "result": "string",       // 步骤执行的结果简述
  "status": "completed" | "failed", // 步骤最终状态
  "referenceIds": ["string"] | null // (可选) 此步骤关联的数据引用 ID 列表
}
```

****

****

****

### 5.3. 错误数据结构
+ `ErrorPayload`** (对应 **`error`** Tag)**

```json
{
  "code": "string",    // 错误码 (例如: "MODEL_ERROR", "TIMEOUT", "INTERNAL_ERROR")
  "message": "string"  // 错误的描述信息
}
```

## 6. 交互流程示例
1. **前端:** 发起请求到后端 `/ask` 接口。
2. **后端:** 接收请求，创建 `SseEmitter`，开始处理。
3. **后端 -> 前端:** 推送 `event: connected`, `data: {"message": "连接已建立", "requestId": "12345"}`
4. **后端 -> 前端:** 推送 `event: inference_start`, `data: {}`
5. **后端:** 执行第一个推理步骤 (例如: 理解意图)。
6. **后端 -> 前端:** 推送 `event: inference_step_start`, `data: {"stepId": "step1", "stepName": "理解意图", "status": "running"}`
7. **后端 -> 前端:** 推送 `event: inference_step_update`, `data: {"stepId": "step1", "updateContent": "正在分析查询...", "mode": "append"}`
8. **后端 -> 前端:** 推送 `event: inference_step_update`, `data: {"stepId": "step1", "updateContent": "分析过程中的更多细节...", "mode": "newline"}`
9. **后端:** 完成第一个推理步骤。
10. **后端 -> 前端:** 推送 `event: inference_step_end`, `data: {"stepId": "step1", "stepName": "理解意图", "result": "意图为查询天气", "status": "completed"}`
11. **后端:** 执行第二个推理步骤 (例如: 搜索信息)，此步骤产生引用。
12. **后端 -> 前端:** 推送 `event: inference_step_start`, `data: {"stepId": "step2", "stepName": "搜索信息", "status": "running"}`
13. **后端 -> 前端:** 推送 `event: inference_step_update`, `data: {"stepId": "step2", "updateContent": "搜索天气 API", "mode": "append"}`
14. **后端 -> 前端:** 推送 `event: inference_step_end`, `data: {"stepId": "step2", "stepName": "搜索信息", "result": "获取到北京天气数据", "status": "completed", "referenceIds": ["ref_weather_beijing"]}`
15. **后端:** ... 其他推理步骤 ...
16. **后端 -> 前端:** 推送 `event: inference_end`, `data: {}`
17. **后端 -> 前端:** 推送 `event: answer_start`, `data: {}`
18. **后端:** 生成最终回答内容。
19. **后端 -> 前端:** 推送 `event: answer_chunk`, `data: {"text": "根据最新数据，北京今天天气晴朗，"}`
20. **后端 -> 前端:** 推送 `event: answer_chunk`, `data: {"text": "气温 25 摄氏度。"}`
21. **后端 -> 前端:** 推送 `event: answer_references`, `data: {"referenceIds": ["ref_weather_beijing"]}`
22. **后端 -> 前端:** 推送 `event: answer_end`, `data: {}`
23. **后端 -> 前端:** 推送 `event: stream_end`, `data: {}`
24. **后端:** 关闭 `SseEmitter`。
25. **前端:** 接收到 `stream_end`，知道流程结束。



![](https://cdn.nlark.com/yuque/0/2025/png/12785224/1744185511806-a1e37c1c-44ad-49c8-919b-dff050883405.png)

## 7. 错误处理
+ 如果在处理过程中发生任何错误，后端应推送一个 `event: error` 事件，包含错误代码和描述信息。
+ 前端接收到 `error` 事件后，应向用户显示错误提示，并可以根据需要中断后续处理。
+ 错误发生后，后端可以选择性地推送 `stream_end` 来结束 SSE 连接。

## 8. 前端实现建议
+ 监听 SSE 连接上的 `message` 事件。
+ 通过 `event.event` 属性获取 Tag (事件类型)。
+ 通过 `event.data` 属性获取 JSON 字符串数据，并解析为 JavaScript 对象。
+ 根据不同的 Tag，更新 UI 的不同部分：
    - `inference_step_start`: 在推理区域创建一个新的步骤卡片，显示初始状态。
    - `inference_step_update`: 根据指定的模式（追加或换行）更新对应步骤的内容。
    - `inference_step_end`: 更新步骤的最终结果和状态，完成步骤卡片的渲染。
    - `inference_step`: (向下兼容) 一次性完成步骤卡片的渲染。
    - `answer_chunk`: 将文本片段追加到最终回答区域。
    - `answer_references`: 存储引用 ID，并在渲染最终回答时，根据这些 ID 高亮或链接到对应的推理步骤或外部来源。
    - `error`: 显示错误信息。
    - `connected`, `inference_start`, `inference_end`, `answer_start`, `answer_end`, `stream_end`: 控制 UI 各区域的加载状态或显隐。

## 9. 步骤生命周期管理
新版设计引入了步骤生命周期管理，每个步骤分为三个阶段：

1. **开始阶段 (Start)**: 使用 `inference_step_start` 标记步骤的开始，包含步骤ID和名称。
2. **更新阶段 (Update)**: 使用 `inference_step_update` 更新步骤的执行内容，支持两种模式：
    - `append`: 在原有内容后追加
    - `newline`: 换行后追加内容
3. **结束阶段 (End)**: 使用 `inference_step_end` 标记步骤的结束，包含步骤的最终结果和状态。

相比单一的 `inference_step` 事件，这种生命周期管理方式更加灵活，能更好地反映步骤的执行过程，特别适合于长时间运行的复杂步骤。


