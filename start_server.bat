@echo off
echo ========================================
echo AI 问答系统启动脚本
echo ========================================
echo.

cd /d "%~dp0"

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请确保Python已安装并添加到PATH
    pause
    exit /b 1
)

echo 正在安装/更新依赖...
pip install -r my_agent/requirements.txt

echo.
echo 正在启动服务器...
echo 服务启动后请访问: http://localhost:8000
echo 按 Ctrl+C 停止服务
echo ========================================
echo.

python run_app.py

pause
