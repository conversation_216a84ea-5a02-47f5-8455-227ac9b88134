from my_agent.utils.state import State  # import state
from langchain_ollama import ChatOllama
from langchain_openai import ChatOpenAI
from my_agent.utils.tools import (
    getMCPTools, 
    build_tools_description, 
    TOOL_MAP, 
    multiplyTool, 
    search_tool
)
from my_agent.utils.prompts import (
    getP<PERSON><PERSON>rompt, 
    get_answer_prompt, 
    get_data_need_prompt, 
    get_answer_analysis_prompt, 
    get_question_rewrite_prompt
)
from langchain_core.messages import (
    ToolMessage, 
    HumanMessage, 
    AIMessage, 
    SystemMessage
)
import json
import re
import logging
import asyncio

logger = logging.getLogger(__name__)

class BasicToolNode:
    """A node that runs the tools requested in the last AIMessage."""
    
    def __init__(self, tools: list) -> None:
        self.tools_by_name = {tool.name: tool for tool in tools}
        
    async def __call__(self, inputs: dict):
        if messages := inputs.get("messages", []):
            message = messages[-1]
        else:
            raise ValueError("No message found in input")
        outputs = []
        for tool_call in message.tool_calls:
            print(f"调用工具: {tool_call['name']}，参数: {tool_call['args']}")
            try:
                tool_result = await self.tools_by_name[tool_call["name"]].ainvoke(
                    tool_call["args"]
                )
                print(f"工具 {tool_call['name']} 返回: {tool_result}")
            except Exception as e:
                error_msg = f"调用工具 {tool_call['name']} 时出错：{str(e)}"
                logger.error(f"Tool call error: {error_msg}", exc_info=True)
                tool_result = error_msg
            
            outputs.append(
                ToolMessage(
                    content=json.dumps(tool_result),
                    name=tool_call["name"],
                    tool_call_id=tool_call["id"],
                )
            )
        print(f"所有工具调用结果: {outputs}")
        return {"messages": outputs}


# 初始化 Ollama 模型
llm_ollama = ChatOllama(
    model="qwen3:30b-a3b",  # 模型名称
    temperature=0.7,  # 可选：设置温度参数
    base_url="http://*************:11434",  # 替换为 Ollama 服务的 IP 地址和端口
)

llm_openai = ChatOpenAI(
    model="TT-14B-R1-0223",
    temperature=0,
    max_tokens=None,
    timeout=None,
    max_retries=2,
    api_key="sk-local",  # if you prefer to pass api key in directly instaed of using env vars
    base_url="http://*************:81/v1",
    streaming=True,
    # organization="...",
    # other params...
)


async def get_tools():
    try:
        mcp_tool_list = await getMCPTools()
        return mcp_tool_list
    except Exception as e:
        print(f"Warning: Failed to get MCP tools: {e}")
        return []


# 定义聊天机器人节点
async def chatbot(state: State):
    propmt = get_answer_prompt(state.get("mcp_payload"), state['user_query'])
    message = await llm_openai.ainvoke([HumanMessage(content=propmt)])
    return {"answer": message.content, "messages": [message]}


# 修改 chatbot_node 为手动解析工具调用
async def chatbot_node(state: dict) -> dict:
    try:
        tools_desc = build_tools_description()
        user_msg = state["user_query"]
        prompt = getPlanPrompt(tools_desc, user_msg)

        ai_msg = await llm_openai.ainvoke([HumanMessage(content=prompt)])
        content = ai_msg.content or ""
        print("Raw LLM ➜", content)

        # 提取 ```json ... ``` 中的 JSON
        json_match = re.search(r"``json\s*({.*?})\s*```", content, re.DOTALL)
        print('json_match:', json_match)
        if not json_match:
            print("未检测到工具调用 JSON，直接返回文本回复")
            return {"messages": [ai_msg]}

        try:
            plan = json.loads(json_match.group(1))
            print('plan:', plan)
        except json.JSONDecodeError as e:
            print("JSON 解析失败:", e)
            return {"messages": [ai_msg]}

        if not plan.get("canCallTool") or not plan.get("tools"):
            print("无需调用工具")
            return {"messages": [ai_msg]}

        # 支持多个工具调用
        result = None
        tool_name = None
        kwargs = None
        for tool in plan["tools"]:
            tool_name = tool["toolName"]
            params_str = tool["params"]
            try:
                kwargs = json.loads(params_str)
            except json.JSONDecodeError:
                print("工具参数 JSON 非法:", params_str)
                continue

            if tool_name not in TOOL_MAP:
                print("未知工具:", tool_name)
                continue

            func = TOOL_MAP[tool_name]
            print(f"调用工具: {tool_name}，参数: {kwargs}")
            try:
                if asyncio.iscoroutinefunction(func):
                    result = await func(**kwargs)
                else:
                    result = func(**kwargs)
                print(f"工具 {tool_name} 返回: {result}")
            except Exception as e:
                import traceback
                traceback.print_exc()
                error_msg = f"调用工具 {tool_name} 时出错：{str(e)}"
                logger.error(f"Tool execution error: {error_msg}", exc_info=True)
                result = error_msg

        return {"mcp_payload": result, "工具名称": tool_name, "调用参数": kwargs, "messages": [result]}
    except Exception as e:
        import traceback
        traceback.print_exc()
        error_msg = f"处理工具调用时发生错误: {str(e)}"
        logger.error(f"Error in chatbot_node: {error_msg}", exc_info=True)
        return {"error": error_msg, "messages": [error_msg]}


# 工具节点
async def tools_node(state: State) -> State:
    last_msg: AIMessage = state["messages"][-1]
    print('last_msg', last_msg)
    outputs = []
    for tc in last_msg.tool_calls:
        tool_name = tc["name"]
        tool_args = tc["args"]
        try:
            if tool_name == "multiply":
                tool_result = multiplyTool(**tool_args)
            elif tool_name == "search":
                tool_result = await search_tool.ainvoke(tool_args)
            else:
                tool_result = f"未知工具: {tool_name}"
        except Exception as e:
            tool_result = f"调用工具 {tool_name} 时出错：{e}"
        outputs.append(
            ToolMessage(
                content=json.dumps(tool_result),
                name=tool_name,
                tool_call_id=tc["id"],
            )
        )
    return {"messages": outputs}


def analysis_input(state: State):
    prompt_data = get_data_need_prompt()
    message = llm_openai.invoke([SystemMessage(content=prompt_data.format(question=state["user_query"]))])
    return {"needs_more_data": message.content, "messages": [message],  "iteration_count": state.get("iteration_count", 0) + 1}


def analysis_result(state):
    prompt_data = get_answer_analysis_prompt()
    answer = state.get("answer", "")
    message = llm_openai.invoke([SystemMessage(content=prompt_data.format(answer=answer, question=state["user_query"]))])
    return {"is_answer_ok": message.content, "messages": [message]}


def combinatorial_problems(state: State):
    prompt_data = get_question_rewrite_prompt()
    answer = state.get("answer", "")
    message = llm_openai.invoke([HumanMessage(content=prompt_data.format(answer=answer, question=state["user_query"]))])
    return {"messages": [message], "user_query": message.content}


def finish_node(state: State):
    return {"iteration_count": 0}

tool_node = None  # 需要在主流程中初始化