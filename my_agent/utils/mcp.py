from langchain_mcp_adapters.client import MultiServerMCPClient
import os

# 从环境变量读取配置，如果不存在则使用默认值
MCP_SERVER_URL = os.getenv('MCP_SERVER_URL', 'http://192.168.210.60:8091/sse')
MCP_SERVER_NAME = os.getenv('MCP_SERVER_NAME', 'ga')
MCP_TRANSPORT = os.getenv('MCP_TRANSPORT', 'sse')

# 配置选项说明
"""
MCP服务器配置选项:
- MCP_SERVER_URL: MCP服务器地址
- MCP_SERVER_NAME: 服务器名称标识
- MCP_TRANSPORT: 传输协议类型 (sse/http等)
"""

client = MultiServerMCPClient(
    {
        MCP_SERVER_NAME: {
            "url": MCP_SERVER_URL,
            "transport": MCP_TRANSPORT,
            # 添加超时配置
            "timeout": 30.0,  # 30秒超时
        }
    }
)