from dotenv import load_dotenv
load_dotenv()

from pydantic import BaseModel, Field
from langchain_core.tools import Tool
from langchain_community.utilities import SearchApiAPIWrapper
from my_agent.utils.mcp import client
from typing import Dict, Any, List, Callable, TypedDict
from langchain_core.tools import StructuredTool
from my_agent.utils.state import State # import state
from langchain_core.messages import ToolMessage, AIMessage
import logging
import asyncio
import time

logger = logging.getLogger(__name__)

# 全局变量：工具名 -> (函数, 参数模型)
TOOL_MAP: Dict[str, Callable] = {}
TOOL_META: Dict[str, Dict[str, Any]] = {}   # 额外存描述和 schema，用来拼提示词

# 把 langchain Tool 转成统一格式
def _register_tool(lc_tool: Tool):
    TOOL_MAP[lc_tool.name] = lc_tool.func
    TOOL_META[lc_tool.name] = {
        "name": lc_tool.name,
        "description": lc_tool.description,
        "schema": lc_tool.args_schema.model_json_schema() if lc_tool.args_schema else {},
    }

class CalculatorInput(BaseModel):
    a:int = Field(description="first number")
    b:int = Field(description="second number")

# 定义工具
def multiplyTool(a:int, b:int) -> int:
    """Multiply two numbers."""
    return a * b

search = SearchApiAPIWrapper()
def search_api(query: str) -> str:
    try:
        return search.run(query=query)
    except Exception as e:
        return f"抱歉，网络搜索暂时不可用：{e}。请稍后重试或检查网络。"
search_tool = StructuredTool.from_function(
    func=search_api,
    name="search",
    description="useful for when you need to search the web"
)

# 所有工具列表
tools = [
    Tool(
        name="multiply",
        func=multiplyTool,
        description="Multiply two integers",
        args_schema=CalculatorInput,
    ),
    search_tool,
]

# 预注册本地工具
_register_tool(
    Tool(
        name="multiply",
        func=multiplyTool,
        description="Multiply two integers",
        args_schema=CalculatorInput,
    )
)
_register_tool(search_tool)

async def load_mcp_tools(max_retries=3, retry_delay=5):
    """
    加载MCP工具，包含重试机制和更好的错误处理
    """
    for attempt in range(max_retries):
        try:
            server_name = "ga"
            async with client.session(server_name) as session:
                tools_resp = await session.list_tools()
                for tool_info in tools_resp.tools:
                    # 只保存名字和 schema，不保存 session
                    TOOL_META[tool_info.name] = {
                        "name": tool_info.name,
                        "description": tool_info.description or "",
                        "schema": tool_info.inputSchema,
                    }
                    # 真正调用时再用新会话
                    async def _make_caller(name):
                        async def _tool_func(**kwargs):
                            async with client.session(server_name) as s:
                                try:
                                    # 设置10秒超时时间
                                    res = await asyncio.wait_for(
                                        s.call_tool(name, arguments=kwargs),
                                        timeout=10.0
                                    )
                                    msg = f"调用工具 {name} 返回: {res}"
                                    print(msg)
                                    # 取第一个文本结果
                                    return res.content[0].text if res.content else ""
                                except asyncio.TimeoutError:
                                    error_msg = f"调用工具 {name} 超时（10秒）"
                                    print(error_msg)
                                    return error_msg
                                except Exception as e:
                                    import traceback
                                    traceback.print_exc()
                                    error_msg = f"调用工具 {name} 时出错：{str(e)}"
                                    print(error_msg)
                                    return error_msg
                        return _tool_func
                    TOOL_MAP[tool_info.name] = await _make_caller(tool_info.name)
                
                logger.info(f"成功加载 {len(tools_resp.tools)} 个MCP工具")
                return True
        except Exception as e:
            logger.warning(f"第 {attempt + 1} 次尝试加载MCP工具失败: {e}")
            if attempt < max_retries - 1:  # 不是最后一次尝试
                logger.info(f"等待 {retry_delay} 秒后重试...")
                await asyncio.sleep(retry_delay)
            else:
                logger.error(f"加载MCP工具失败，已重试 {max_retries} 次: {e}")
                return False

def build_tools_description() -> str:
    lines = []
    for meta in TOOL_META.values():
        lines.append(f"- {meta['name']}{meta['schema']} -> 返回值")
    return "\n".join(lines)

async def getMCPTools():
    try:
        return await client.get_tools()
    except Exception as e:
        logger.warning(f"Failed to get MCP tools: {e}")
        return []