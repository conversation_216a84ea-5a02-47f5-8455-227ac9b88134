"""
推理步骤事件处理逻辑
"""
import json
import uuid
from typing import Any, Dict, Optional


def get_pretty_step_name(name: str) -> str:
    """
    获取步骤的友好显示名称
    
    Args:
        name: 步骤原始名称
        
    Returns:
        str: 友好显示名称
    """
    return {
        "LangGraph": "推理开始",
        "analysis_input": "用户问题分析",
        "ChatOpenAI": "调用模型分析",
        "route_input": "是否需要更多信息",
        "generaterAnswer": "答案生成",
        "route_result": "答案是否改写",
        "analysis_result": "答案分析",
        "mcpAgent": "工具代理",
        "combinatorial_problems": "用户问题改写",
        "finish_node": "正式答案输出",
    }.get(name, name)

def extract_message_content(message: Any) -> str:
    """
    安全地从消息对象中提取内容
    
    Args:
        message: 可能是字符串、字典或具有content属性的对象
        
    Returns:
        str: 提取到的内容字符串
    """
    if hasattr(message, 'content'):
        return getattr(message, 'content', '')
    elif isinstance(message, dict):
        return message.get('content', str(message))
    else:
        return str(message)

def process_inference_step_events(event: Dict[str, Any], step_id_map: Dict[str, str]) -> str:
    """
    处理推理步骤的事件
    
    Args:
        event: 事件数据
        step_id_map: 步骤ID映射表
        
    Returns:
        str: SSE事件字符串
    """
    kind = event["event"]
    name = event.get("name", "")
    langgraph_node = event.get("metadata", {}).get("langgraph_node", "")
    langgraph_step = event.get("metadata", {}).get("langgraph_step", str(uuid.uuid4()))  # 修复可能的 KeyError
    step_name = get_pretty_step_name(name)
    
    # 判断是否是正式回答节点 (finish_node)
    is_answer_node = name == "finish_node"
    
    # 判断是否是推理步骤节点（1-5步）
    is_inference_step = name in ["analysis_input", "generaterAnswer", "mcpAgent", "analysis_result", "combinatorial_problems"]
    
    is_route_node = name == "route_input" or name == "route_result"

    # 只处理推理步骤、回答节点和路由节点
    if not is_inference_step and not is_answer_node and not is_route_node:
        return None

    # 为每个步骤生成唯一的step_id，确保开始和结束使用相同的ID
    step_key = f"{langgraph_node}:{langgraph_step}"
    if step_key not in step_id_map:
        step_id_map[step_key] = str(uuid.uuid4())
    step_id = step_id_map[step_key]
    
    # 处理路由节点的特殊情况 - 关联到对应的推理步骤
    if is_route_node:
        # 确定关联的推理步骤
        associated_step = ""
        if langgraph_node == "analysis_input" and name == "route_input":
            associated_step = "analysis_input"
        elif langgraph_node == "analysis_result" and name == "route_result":
            associated_step = "analysis_result"
        
        if associated_step:
            # 使用关联步骤的step_id
            associated_step_key = f"on_chain_stream:{associated_step}:{associated_step}"
            if associated_step_key in step_id_map:
                step_id = step_id_map[associated_step_key]
    
    # 处理不同类型的事件
    if is_inference_step and "_start" in kind:
        # 推理节点开始
        return f"event: inference_step_start\ndata: {json.dumps({'stepId': step_id, 'stepName': step_name, 'status': 'running'})}\n\n"
    
    elif is_inference_step and "_stream" in kind:
        update_content = ""
        
        # 根据不同情况处理流式内容
        if name == "analysis_input" and "_stream" in kind:
            update_content = "正在分析是否需要更多信息"
        elif name == "analysis_result" and "_stream" in kind:
            update_content = "正在分析答案是否符合预期"
        elif name == "combinatorial_problems" and "_stream" in kind:
            update_content = "即将改写用户问题"
        elif langgraph_node == "analysis_input" and name == "route_input":
            # 根据needs_more_data的值返回不同的结果
            update_content = "需要更多信息，将调用工具代理获取更多信息"  # 默认值
        elif langgraph_node == "analysis_result" and name == "route_result":
            # 根据is_answer_ok的值返回不同的结果
            update_content = "答案符合预期，直接返回"  # 默认值
        elif is_inference_step and "_stream" in kind:
            # 处理其他步骤的流式内容
            chunk_data = event["data"]["chunk"]
            if hasattr(chunk_data, 'content'):
                update_content = chunk_data.content or ""
            elif isinstance(chunk_data, dict):
                update_content = chunk_data.get("content", str(chunk_data))
            else:
                update_content = str(chunk_data)
        
        # 只有当有内容时才发送更新事件
        if update_content:
            return f"event: inference_step_update\ndata: {json.dumps({'stepId': step_id, 'updateContent': update_content, 'mode': 'append'})}\n\n"
    
    elif is_inference_step and "_end" in kind:
        result = ""
        if isinstance(event["data"]["output"], dict) and "messages" in event["data"]["output"]:
            last_message = event["data"]["output"]["messages"][-1]
            # 安全地提取消息内容
            if hasattr(last_message, 'content'):
                result = getattr(last_message, 'content', '')
            elif isinstance(last_message, dict):
                result = last_message.get('content', str(last_message))
            else:
                result = str(last_message)
        
        # 特殊处理某些步骤的结束结果
        if name == "analysis_input":
            # 根据needs_more_data的值返回不同的结果
            if isinstance(event["data"]["output"], dict):
                needs_more_data = event["data"]["output"].get("needs_more_data", "yes")
                if needs_more_data == "no":
                    result = "不需要更多信息，将直接生成答案"
                else:
                    result = "需要更多信息，将调用工具代理获取更多信息"
        
        if name == "analysis_result":
            # 根据is_answer_ok的值返回不同的结果
            if isinstance(event["data"]["output"], dict):
                is_answer_ok = event["data"]["output"].get("is_answer_ok", "no")
                if is_answer_ok == "yes":
                    result = "答案符合预期，直接返回"
                else:
                    result = "答案不符合预期，将改写用户问题"
        
        if name == "combinatorial_problems":
            result = "用户问题改写完成"
            
            # 检查是否有改写后的问题，如果有则返回额外信息
            if isinstance(event["data"]["output"], dict):
                rewritten_query = event["data"]["output"].get("user_query")
                if rewritten_query:
                    # 将改写后的问题打印到控制台
                    print(f"改写后的问题: {rewritten_query}")
                    # 将改写后的问题追加到result中
                    result += f"\n改写后的问题: {rewritten_query}"
        
        # 发送步骤结束事件
        return f"event: inference_step_end\ndata: {json.dumps({'stepId': step_id, 'stepName': step_name, 'result': result, 'status': 'completed'})}\n\n"
    
    return None