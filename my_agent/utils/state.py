from typing_extensions import TypedDict, Annotated
from langgraph.graph.message import add_messages



# 定义状态类型
class State(TypedDict):
    messages: Annotated[list, add_messages]
    user_query: str                                         # 当前用户原始问题
    needs_more_data: bool | None                            # 是否需要更多信息
    mcp_payload: str | None                                 # mcp工具返回结果
    answer: str | None                                      # 最终答案
    is_answer_ok: bool | None                               # 答案是否正确
    iteration_count: int