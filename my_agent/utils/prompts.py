
def getPlanPrompt(tools_desc, user_msg):
    schema_json = """
    {
        "type": "object",
        "properties": {
        "tools": {
            "type": "array",
            "items": {
            "type": "object",
            "properties": {
                "toolName": {
                "type": "string",
                "description": "工具名"
                },
                "description": {
                "type": "string",
                "description": "工具描述"
                },
                "stepName": {
                "type": "string",
                "description": "步骤描述"
                },
                "reason": {
                "type": "string",
                "description": "使用原因"
                },
                "params": {
                "type": "string",
                "description": "工具调用参数，符合工具inputSchema要求（json字符串）"
                }
            },
            "required": [
                "toolName",
                "description",
                "stepName",
                "reason",
                "params"
            ]
            },
            "description": "工具列表"
        },
        "canCallTool": {
            "type": "boolean",
            "description": "是否存在可调度的工具"
        }
        },
        "required": [
        "tools",
        "canCallTool"
        ]
    }
""".strip()
    myPropt = f"""
    请根据用户输入的内容，规划用户应该调度哪一些mcp工具（一个或多个）并按照并按照json格式返回

    ## 请遵循以下指导原则：

    - 只进行任务规划，即：规划应该使用哪些工具，不参与其他业务分析
    - 规划工具时需要判断用户输入的内容是否可以构造出工具所需参数，如果无法构造，则暂不规划调度该工具，等待后续规划
    - 参数只能从用户输入的内容中提取，不能胡编乱造，也不能用占位符标识还未得到的数据
    - 使用工具时提供符合其模式文档的有效参数
    - 优雅地处理错误，理解错误原因并使用修正后的参数重试
    - 逐步完成用户请求，使用最合适的工具
    - 尽可能避免重复规划的工具调用
    - 同一个工具需要避免重复执行相同参数的调度

    ## 响应参数（json-schema）

    {schema_json}

    用户输入内容：{user_msg}
    可使用的工具如下：
    {tools_desc}
    """
    return myPropt


def get_answer_prompt(mcp_payload, user_query):
    """
    生成回答用户问题的提示词
    """
    if mcp_payload is not None:
        return f"工具返回：{mcp_payload}，用户的原始问题是：{user_query}，请用自然语言回答用户。"
    else:
        return f"用户的原始问题是：{user_query}，请用自然语言回答用户。"


def get_data_need_prompt():
    """
    生成判断是否需要更多数据的提示词
    """
    return (
        "你是一个智能助手。请判断用户提出的问题，是否需要更多的数据才能给出准确答案。"
        "只回答 'yes' 或 'no'，不要输出其它内容。\n"
        "问题：{question}"
    )


def get_answer_analysis_prompt():
    """
    生成分析答案是否正确的提示词
    """
    return (
        "根据用户的问题和答案，判断答案是否正确。"
        "只回答 'yes' 或 'no'，不要输出其它内容。\n"
        "问题：{question}\n"
        "答案：{answer}"
    )


def get_question_rewrite_prompt():
    """
    生成改写问题的提示词
    """
    return (
        "根据用户的问题和答案改写用户问题，并只返回改写问题的内容。\n"
        "问题：{question}\n"
        "答案：{answer}"
    )