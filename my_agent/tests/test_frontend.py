#!/usr/bin/env python3
"""
测试前端功能的脚本
"""

import requests
import json
import time
from pathlib import Path

def test_static_files():
    """测试静态文件是否可以正常访问"""
    print("测试静态文件访问...")
    
    # 检查静态文件是否存在
    static_dir = Path(__file__).parent / "static"
    index_file = static_dir / "index.html"
    
    if not index_file.exists():
        print("❌ 错误: index.html 文件不存在")
        return False
    
    print("✅ 静态文件存在")
    return True

def test_api_endpoints():
    """测试API端点是否正常"""
    base_url = "http://localhost:8000"
    
    print("测试API端点...")
    
    try:
        # 测试根路径
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✅ 前端页面可访问")
        else:
            print(f"❌ 前端页面访问失败: {response.status_code}")
            return False
            
        # 测试API根路径
        response = requests.get(f"{base_url}/api/", timeout=5)
        if response.status_code == 200:
            print("✅ API根路径正常")
        else:
            print(f"❌ API根路径失败: {response.status_code}")
            
        # 测试创建请求ID
        response = requests.post(f"{base_url}/api/createRequestId", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if "requestId" in data:
                print("✅ 创建请求ID正常")
            else:
                print("❌ 创建请求ID响应格式错误")
        else:
            print(f"❌ 创建请求ID失败: {response.status_code}")
            
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务已启动")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def test_chat_endpoint():
    """测试聊天端点（简单测试，不测试SSE流）"""
    base_url = "http://localhost:8000"
    
    print("测试聊天端点...")
    
    try:
        # 准备测试数据
        test_data = {
            "messages": "你好",
            "thread_id": "test_thread"
        }
        
        # 发送请求（只测试连接，不等待完整响应）
        response = requests.post(
            f"{base_url}/api/chat",
            json=test_data,
            timeout=3,
            stream=True
        )
        
        if response.status_code == 200:
            print("✅ 聊天端点可访问")
            return True
        else:
            print(f"❌ 聊天端点失败: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("✅ 聊天端点正常（超时是预期的，因为是流式响应）")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到聊天端点")
        return False
    except Exception as e:
        print(f"❌ 测试聊天端点时发生错误: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("AI 问答系统前端测试")
    print("=" * 50)
    
    # 测试静态文件
    if not test_static_files():
        print("\n❌ 静态文件测试失败，请检查文件是否存在")
        return
    
    print("\n" + "-" * 30)
    
    # 测试API端点（需要服务运行）
    print("注意: 以下测试需要服务正在运行")
    print("如果服务未启动，请先运行: python run_server.py")
    print()
    
    time.sleep(1)
    
    if test_api_endpoints():
        print("\n" + "-" * 30)
        test_chat_endpoint()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("如果所有测试通过，您可以在浏览器中访问: http://localhost:8000")
    print("=" * 50)

if __name__ == "__main__":
    main()
