from langgraph.graph import StateGraph, START, END
from my_agent.utils.state import State # import state
from my_agent.utils.nodes import chatbot_node, chatbot, analysis_input, analysis_result, combinatorial_problems, finish_node
from langgraph.checkpoint.memory import MemorySaver
from my_agent.utils.tools import load_mcp_tools

import asyncio
import logging

logger = logging.getLogger(__name__)

async def build_graph():
    try:
        # 尝试加载MCP工具，即使失败也继续启动应用
        mcp_loaded = await load_mcp_tools()
        if mcp_loaded:
            logger.info("MCP tools loaded successfully")
        else:
            logger.warning("Failed to load MCP tools. Continuing without MCP tools.")
    except Exception as e:
        logger.warning(f"Failed to load MCP tools: {e}. Continuing without MCP tools.")
        # 如果MCP工具加载失败，继续启动应用但不加载MCP工具
        pass
    
    graph_builder = StateGraph(State)
    graph_builder.add_node("mcpAgent", chatbot_node)
    graph_builder.add_node("generaterAnswer", chatbot)
    graph_builder.add_node("analysis_input", analysis_input)
    graph_builder.add_node("analysis_result", analysis_result)
    graph_builder.add_node("combinatorial_problems", combinatorial_problems)
    graph_builder.add_node("finish_node", finish_node)

    graph_builder.add_edge(START, "analysis_input")
    def route_input(state: State):
        if state['needs_more_data'] == 'yes':
            return True
        else:
            return False
    graph_builder.add_conditional_edges(
        "analysis_input",
        route_input,
        {True: "mcpAgent", False: "generaterAnswer"}
    )
    graph_builder.add_edge("mcpAgent", "generaterAnswer")
    graph_builder.add_edge("generaterAnswer", "analysis_result")
    def route_result(state: State):
        iteration_count = state.get("iteration_count", 0)
        if state['is_answer_ok'] == 'yes' or iteration_count >= 3:
            print('conut', iteration_count)
            return False
        else:
            return True
    graph_builder.add_conditional_edges(
        'analysis_result',
        route_result,
        {True: "combinatorial_problems", False: "finish_node"}
    )
    graph_builder.add_edge("combinatorial_problems", "analysis_input")
    graph_builder.add_edge("finish_node", END)
    memory = MemorySaver()
    graph = graph_builder.compile(checkpointer=memory)
    return graph