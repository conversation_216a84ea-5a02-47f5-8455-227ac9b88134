#!/usr/bin/env python3
"""
启动FastAPI服务器的脚本
"""

import uvicorn
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

if __name__ == "__main__":
    print("正在启动AI问答服务...")
    print("服务启动后，请访问: http://localhost:8000")
    print("API文档地址: http://localhost:8000/docs")
    print("按 Ctrl+C 停止服务")
    print("-" * 50)
    
    uvicorn.run(
        "apiServer:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
