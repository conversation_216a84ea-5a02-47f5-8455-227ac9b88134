# AI 问答系统前端

## 功能特性

- 🎨 现代化的聊天界面设计
- 💬 实时流式对话体验
- 🔄 推理过程可视化
- 📱 响应式设计，支持移动端
- ⚡ 基于Server-Sent Events (SSE) 的实时通信

## 快速开始

### 1. 安装依赖

确保已安装所有必要的Python包：

```bash
pip install -r requirements.txt
```

### 2. 启动服务

有两种方式启动服务：

**方式一：使用启动脚本（推荐）**
```bash
cd my_agent
python run_server.py
```

**方式二：直接使用uvicorn**
```bash
cd my_agent
uvicorn apiServer:app --host 0.0.0.0 --port 8000 --reload
```

### 3. 访问前端

服务启动后，在浏览器中访问：
- 前端页面: http://localhost:8000
- API文档: http://localhost:8000/docs

## 界面说明

### 主要功能区域

1. **聊天头部**: 显示应用标题
2. **状态指示器**: 显示连接状态和处理状态
3. **消息区域**: 显示对话历史
4. **推理过程**: 显示AI的思考步骤（可选）
5. **输入区域**: 输入问题和发送按钮

### 交互方式

- 在输入框中输入问题
- 点击"发送"按钮或按Enter键发送
- 等待AI回复，可以看到实时的推理过程
- 支持多轮对话

## 技术实现

### 前端技术
- 纯HTML/CSS/JavaScript实现
- 使用Server-Sent Events (SSE) 进行实时通信
- 响应式设计，适配各种屏幕尺寸

### 后端集成
- FastAPI静态文件服务
- SSE流式响应
- 推理过程事件处理

### API接口

主要使用的API端点：
- `GET /`: 前端页面
- `POST /api/chat`: 聊天接口（SSE流式响应）
- `POST /api/createRequestId`: 创建请求ID
- `GET /api/mermaid`: 获取流程图

## 自定义配置

### 修改端口
在 `run_server.py` 中修改端口：
```python
uvicorn.run(
    "apiServer:app",
    host="0.0.0.0",
    port=8080,  # 修改为你想要的端口
    reload=True,
    log_level="info"
)
```

### 修改样式
编辑 `static/index.html` 中的CSS样式来自定义界面外观。

## 故障排除

### 常见问题

1. **端口被占用**
   - 修改端口号或停止占用端口的程序

2. **静态文件无法加载**
   - 确保 `static` 目录存在且包含 `index.html`

3. **SSE连接失败**
   - 检查防火墙设置
   - 确保后端服务正常运行

### 日志查看

服务运行时会在控制台显示详细日志，包括：
- 请求处理过程
- 错误信息
- 推理步骤

## 开发说明

如需修改前端功能，主要文件：
- `static/index.html`: 前端页面和逻辑
- `apiServer.py`: 后端API和静态文件服务
- `run_server.py`: 启动脚本
