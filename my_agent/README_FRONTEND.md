# AI 问答系统前端

## 功能特性

- 🎨 现代化的聊天界面设计
- 💬 实时流式对话体验
- 🔄 详细的推理过程可视化
- 📊 步骤生命周期管理（开始、更新、结束）
- 🔧 工具调用显示
- 📚 引用和参考链接支持
- 📱 响应式设计，支持移动端
- ⚡ 完整的SSE事件处理
- 🚨 错误处理和状态指示

## 快速开始

### 1. 安装依赖

确保已安装所有必要的Python包：

```bash
pip install -r requirements.txt
```

### 2. 启动服务

有两种方式启动服务：

**方式一：使用启动脚本（推荐）**
```bash
cd my_agent
python run_server.py
```

**方式二：直接使用uvicorn**
```bash
cd my_agent
uvicorn apiServer:app --host 0.0.0.0 --port 8000 --reload
```

### 3. 访问前端

服务启动后，在浏览器中访问：
- 前端页面: http://localhost:8000
- API文档: http://localhost:8000/docs
- 测试页面: http://localhost:8000/static/test.html

## 界面说明

### 主要功能区域

1. **聊天头部**: 显示应用标题
2. **状态指示器**: 显示连接状态和处理状态
3. **消息区域**: 显示对话历史
4. **推理过程**: 显示AI的思考步骤（可选）
5. **输入区域**: 输入问题和发送按钮

### 交互方式

- 在输入框中输入问题
- 点击"发送"按钮或按Enter键发送
- 实时查看AI的推理过程：
  - 步骤开始时显示步骤名称和状态
  - 步骤执行过程中显示更新内容
  - 步骤完成时显示结果和引用
  - 工具调用时显示调用详情
- 流式接收AI回答
- 查看引用和参考链接
- 支持多轮对话

### 测试功能

访问 http://localhost:8000/static/test.html 可以：
- 测试各种SSE事件的处理
- 模拟完整的推理和回答流程
- 查看事件日志和界面效果
- 验证错误处理机制

## 技术实现

### 前端技术
- 纯HTML/CSS/JavaScript实现
- 完整的SSE事件处理系统
- 步骤生命周期管理
- 响应式设计，适配各种屏幕尺寸

### 支持的SSE事件类型
根据《大模型问答 SSE 通信格式概要设计.md》实现：

**连接事件:**
- `connected`: 连接建立
- `stream_end`: 流结束

**推理过程事件:**
- `inference_start`: 推理开始
- `inference_step_start`: 步骤开始
- `inference_step_update`: 步骤更新（支持append和newline模式）
- `inference_step_end`: 步骤结束
- `inference_end`: 推理结束

**回答过程事件:**
- `answer_start`: 回答开始
- `answer_chunk`: 回答片段
- `answer_references`: 引用信息
- `answer_more_link`: 更多链接
- `answer_end`: 回答结束

**错误处理:**
- `error`: 错误信息

### 后端集成
- FastAPI静态文件服务
- SSE流式响应
- 完整的推理过程事件处理

### API接口

主要使用的API端点：
- `GET /`: 前端页面
- `POST /api/chat`: 聊天接口（SSE流式响应）
- `POST /api/createRequestId`: 创建请求ID
- `GET /api/mermaid`: 获取流程图
- `GET /static/test.html`: 测试页面

## 自定义配置

### 修改端口
在 `run_server.py` 中修改端口：
```python
uvicorn.run(
    "apiServer:app",
    host="0.0.0.0",
    port=8080,  # 修改为你想要的端口
    reload=True,
    log_level="info"
)
```

### 修改样式
编辑 `static/index.html` 中的CSS样式来自定义界面外观。

## 故障排除

### 常见问题

1. **端口被占用**
   - 修改端口号或停止占用端口的程序

2. **静态文件无法加载**
   - 确保 `static` 目录存在且包含 `index.html`

3. **SSE连接失败**
   - 检查防火墙设置
   - 确保后端服务正常运行

### 日志查看

服务运行时会在控制台显示详细日志，包括：
- 请求处理过程
- 错误信息
- 推理步骤

## 开发说明

如需修改前端功能，主要文件：
- `static/index.html`: 前端页面和逻辑
- `apiServer.py`: 后端API和静态文件服务
- `run_server.py`: 启动脚本
