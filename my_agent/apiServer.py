import json
import logging
import uuid
from contextlib import asynccontextmanager

from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import PlainTextResponse, StreamingResponse
from langchain_core.messages import AIMessage

from my_agent.agent import build_graph
from my_agent.utils.event_handler import process_inference_step_events

# 配置日志
logger = logging.getLogger(__name__)

# ---------- 单例 ----------
graph = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI 启动/关闭事件"""
    global graph
    try:
        graph = await build_graph()   # 只执行一次
        logger.info("Graph built successfully")
    except Exception as e:
        logger.error(f"Failed to build graph: {e}")
        raise
    yield
    # 如果有需要，在这里关闭资源

app = FastAPI(lifespan=lifespan)

# ---------- 路由 ----------
@app.get("/api/")
def root():
    return {"msg": "hello fastapi"}

@app.post("/api/createRequestId")
async def create_item():
    print('获取会话id')
    return {"requestId": str(uuid.uuid4())}

@app.get("/api/mermaid", response_class=PlainTextResponse)
async def get_mermaid():
    mermaid_code = graph.get_graph().draw_mermaid()
    return mermaid_code

@app.post("/api/chat")
async def chat(request: Request):

    try:
        body = await request.json()
    except Exception:
        raise HTTPException(status_code=400, detail="Invalid JSON body")

    msgs = body.get("messages")
    if not msgs:
        raise HTTPException(status_code=400, detail="Missing 'messages' field")
    thread_id = body.get("thread_id", "default")
    config = {"configurable": {"thread_id": thread_id}}
    request_id = str(uuid.uuid4())

    async def event_stream():
        # 1. 连接建立
        yield f"event: connected\ndata: {json.dumps({'message': '连接已建立', 'requestId': request_id})}\n\n"
        
        # 标记是否已发送推理开始事件
        inference_started = False
        # 标记是否已发送回答开始事件
        answer_started = False
        events = []
        # 为每个步骤维护唯一的stepId
        step_id_map = {}
        
        try:
            async for event in graph.astream_events({"user_query": msgs}, config, version="v2"):
                events.append(event)
                
                # 使用event_handler模块处理推理步骤事件
                inference_event = process_inference_step_events(event, step_id_map)
                if inference_event:
                    # 检查是否有改写后的问题需要单独发送事件
                    if "rewrittenQuery" in inference_event:
                        # 解析事件数据，发送改写后问题的独立事件
                        try:
                            # 提取事件数据
                            event_data = inference_event.split("data: ")[1].split("\n\n")[0]
                            event_json = json.loads(event_data)
                            if "rewrittenQuery" in event_json:
                                rewritten_query = event_json["rewrittenQuery"]
                                # 发送改写后问题的事件
                                yield f"event: rewritten_query\ndata: {json.dumps({'query': rewritten_query})}\n\n"
                        except (IndexError, json.JSONDecodeError, KeyError):
                            # 如果解析失败，继续正常流程
                            pass
                    yield inference_event
                
                name = event.get("name", "")
                
                # 判断是否是正式回答节点 (finish_node)
                is_answer_node = name == "finish_node"
                
                # 处理回答节点
                if is_answer_node:
                    # 正式回答节点开始前发送answer_start事件
                    if not answer_started:
                        yield f"event: answer_start\ndata: {json.dumps({})}\n\n"
                        answer_started = True

            # 发送推理开始事件（如果还没有发送）
            if not inference_started:
                yield f"event: inference_start\ndata: {json.dumps({})}\n\n"

            # 发送回答开始事件（如果还没有发送）
            if not answer_started:
                yield f"event: answer_start\ndata: {json.dumps({})}\n\n"
            # 6. 从之前的事件流中获取最终回答
            final_msg = None
            final_answer = ""
            # 查找最终的答案内容
            for event in reversed(events):
                if event["event"] == "on_chain_end":
                    output = event["data"].get("output", {})
                    if isinstance(output, dict) and "answer" in output:
                        final_answer = output["answer"]
                        break
            
            # 如果没有找到answer字段，则尝试查找finish_node的输出
            if not final_answer:
                for event in reversed(events):
                    if event.get("name") == "finish_node" and event["event"] == "on_chain_end":
                        output = event["data"].get("output", {})
                        if isinstance(output, dict) and "messages" in output:
                            final_msg = output["messages"][-1]
                            if hasattr(final_msg, 'content'):
                                final_answer = final_msg.content
                            break

            # 逐字符/逐句流式推送最终答案
            if final_answer:
                for ch in final_answer:
                    yield f"event: answer_chunk\ndata: {json.dumps({'text': ch})}\n\n"

                # 7. 引用（若有）
                if isinstance(final_msg, AIMessage):
                    refs = final_msg.response_metadata.get("citations", [])
                    if refs:
                        yield f"event: answer_references\ndata: {json.dumps({'referenceIds': refs})}\n\n"
        except Exception as e:
            # 工具调用或其他处理过程中发生异常时，通过SSE返回错误信息
            import traceback
            traceback.print_exc()
            error_message = f"处理请求时发生错误: {str(e)}"
            yield f"event: error\ndata: {json.dumps({'message': error_message})}\n\n"
            logger.error(f"Error in event stream: {e}", exc_info=True)
        
        # 确保流结束事件总是发送
        yield f"event: answer_end\ndata: {json.dumps({})}\n\n"
        yield f"event: stream_end\ndata: {json.dumps({})}\n\n"
        
        # 生成并打印本次请求的流程日志
        try:
            process_log = []
            process_log.append("=== 请求处理流程日志 ===")
            process_log.append(f"请求ID: {request_id}")
            process_log.append(f"用户消息: {msgs}")
            
            for i, event in enumerate(events):
                event_type = event.get("event", "unknown")
                event_name = event.get("name", "unnamed")
                event_metadata = event.get("metadata", {})
                event_data = event.get("data", {})
                
                # 只处理_start和_end事件，忽略_stream事件
                if event_type.endswith("_start") or event_type.endswith("_end"):
                    # 添加事件基本信息
                    log_entry = f"{i+1}. 事件类型: {event_type}, 名称: {event_name}"
                    
                    # 添加节点信息
                    if "langgraph_node" in event_metadata:
                        log_entry += f", 节点: {event_metadata['langgraph_node']}"
                    
                    # 添加输入或输出结果信息（如果存在）
                    if event_type.endswith("_start") and "input" in event_data:
                        input_data = event_data["input"]
                        if isinstance(input_data, dict):
                            # 简化输入数据的显示
                            log_entry += f", 输入: {str(input_data)[:200]}..."  # 只显示前200个字符
                        elif isinstance(input_data, str):
                            log_entry += f", 输入: {input_data[:200]}..."  # 只显示前200个字符
                    
                    if event_type.endswith("_end") and "output" in event_data:
                        output = event_data["output"]
                        if isinstance(output, dict):
                            if "answer" in output:
                                log_entry += f", 答案: {output['answer'][:100]}..."  # 只显示前100个字符
                            elif "messages" in output and len(output["messages"]) > 0:
                                last_message = output["messages"][-1]
                                if hasattr(last_message, 'content'):
                                    content = getattr(last_message, 'content', '')
                                    log_entry += f", 消息: {content[:100]}..."  # 只显示前100个字符
                                elif isinstance(last_message, str):
                                    log_entry += f", 消息: {last_message[:100]}..."  # 只显示前100个字符
                            elif "user_query" in output:
                                log_entry += f", 用户查询: {output['user_query'][:100]}..."  # 只显示前100个字符
                            else:
                                # 显示其他输出内容
                                log_entry += f", 输出: {str(output)[:100]}..."  # 只显示前100个字符
                        elif isinstance(output, str):
                            log_entry += f", 输出: {output[:100]}..."  # 只显示前100个字符
                    
                    process_log.append(log_entry)
            
            process_log.append("=== 流程日志结束 ===")
            
            # 打印流程日志到控制台
            for log_line in process_log:
                print(log_line)
                
        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"生成流程日志时发生错误: {str(e)}")

    headers = {
        "Cache-Control": "no-cache",
        "X-Accel-Buffering": "no",
    }
    return StreamingResponse(event_stream(), media_type="text/event-stream", headers=headers)