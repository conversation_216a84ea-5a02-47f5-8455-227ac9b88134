<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE 事件测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #5a67d8;
        }
        
        .event-log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .event-item {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }
        
        .event-connected { background: #d4edda; }
        .event-inference { background: #cce5ff; }
        .event-answer { background: #fff3cd; }
        .event-error { background: #f8d7da; }
        .event-end { background: #e2e3e5; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>SSE 事件测试页面</h1>
        <p>这个页面用于测试前端是否能正确处理各种SSE事件类型。</p>
        
        <div>
            <button class="test-button" onclick="testConnection()">测试连接事件</button>
            <button class="test-button" onclick="testInferenceFlow()">测试推理流程</button>
            <button class="test-button" onclick="testAnswerFlow()">测试回答流程</button>
            <button class="test-button" onclick="testErrorEvent()">测试错误事件</button>
            <button class="test-button" onclick="clearLog()">清空日志</button>
        </div>
        
        <h3>事件日志:</h3>
        <div id="eventLog" class="event-log"></div>
        
        <h3>模拟聊天界面:</h3>
        <div id="chatContainer" style="border: 1px solid #ccc; padding: 10px; min-height: 200px; background: white;">
            <!-- 这里会显示模拟的聊天内容 -->
        </div>
    </div>

    <script>
        let eventLog = document.getElementById('eventLog');
        let chatContainer = document.getElementById('chatContainer');
        
        // 简化版的ChatApp类用于测试
        class TestChatApp {
            constructor() {
                this.inferenceSteps = new Map();
                this.currentInferenceSection = null;
                this.currentAnswerDiv = null;
                this.currentAnswer = '';
                this.answerReferences = [];
            }
            
            logEvent(event, data) {
                const eventItem = document.createElement('div');
                eventItem.className = 'event-item';
                
                // 根据事件类型设置样式
                if (event.includes('connected')) eventItem.classList.add('event-connected');
                else if (event.includes('inference')) eventItem.classList.add('event-inference');
                else if (event.includes('answer')) eventItem.classList.add('event-answer');
                else if (event.includes('error')) eventItem.classList.add('event-error');
                else if (event.includes('end')) eventItem.classList.add('event-end');
                
                eventItem.textContent = `${new Date().toLocaleTimeString()} - ${event}: ${JSON.stringify(data)}`;
                eventLog.appendChild(eventItem);
                eventLog.scrollTop = eventLog.scrollHeight;
            }
            
            addMessage(content, isUser = false) {
                const messageDiv = document.createElement('div');
                messageDiv.style.cssText = `
                    margin: 10px 0;
                    padding: 10px;
                    border-radius: 10px;
                    ${isUser ? 'background: #667eea; color: white; text-align: right;' : 'background: #f1f1f1;'}
                `;
                messageDiv.textContent = content;
                chatContainer.appendChild(messageDiv);
                return messageDiv;
            }
            
            createInferenceSection() {
                const section = document.createElement('div');
                section.style.cssText = `
                    margin: 15px 0;
                    padding: 15px;
                    background: #f8f9fa;
                    border-left: 4px solid #667eea;
                    border-radius: 8px;
                `;
                section.innerHTML = '<strong>推理过程:</strong>';
                chatContainer.appendChild(section);
                return section;
            }
            
            createStepCard(stepId, stepName, status = 'running') {
                const stepCard = document.createElement('div');
                stepCard.style.cssText = `
                    margin: 10px 0;
                    padding: 12px;
                    background: white;
                    border: 1px solid #e1e5e9;
                    border-radius: 6px;
                    border-left: 4px solid ${status === 'completed' ? '#28a745' : status === 'failed' ? '#dc3545' : '#ffc107'};
                `;
                stepCard.innerHTML = `
                    <div><strong>${stepName}</strong> <span style="font-size: 12px; color: #666;">[${status}]</span></div>
                    <div class="step-content" style="margin-top: 5px; color: #666;"></div>
                `;
                return stepCard;
            }
            
            // 实现其他必要的方法...
            updateStepContent(stepId, content, mode = 'append') {
                const stepCard = this.inferenceSteps.get(stepId);
                if (!stepCard) return;
                
                const contentDiv = stepCard.querySelector('.step-content');
                if (mode === 'newline') {
                    contentDiv.textContent += '\n' + content;
                } else {
                    contentDiv.textContent += content;
                }
            }
            
            finishStep(stepId, stepName, result, status, referenceIds = null) {
                const stepCard = this.inferenceSteps.get(stepId);
                if (!stepCard) return;
                
                // 更新边框颜色
                const color = status === 'completed' ? '#28a745' : status === 'failed' ? '#dc3545' : '#ffc107';
                stepCard.style.borderLeftColor = color;
                
                // 更新状态显示
                const statusSpan = stepCard.querySelector('span');
                statusSpan.textContent = `[${status}]`;
                
                // 添加结果
                if (result) {
                    const resultDiv = document.createElement('div');
                    resultDiv.style.cssText = 'margin-top: 8px; padding: 8px; background: #f8f9fa; border-radius: 4px; font-style: italic;';
                    resultDiv.textContent = `结果: ${result}`;
                    stepCard.appendChild(resultDiv);
                }
            }
            
            handleSSEEvent(event, data) {
                this.logEvent(event, data);
                
                switch (event) {
                    case 'connected':
                        break;
                        
                    case 'inference_start':
                        this.currentInferenceSection = this.createInferenceSection();
                        this.inferenceSteps.clear();
                        break;
                        
                    case 'inference_step_start':
                        if (data.stepId && data.stepName) {
                            const stepCard = this.createStepCard(data.stepId, data.stepName, data.status || 'running');
                            this.inferenceSteps.set(data.stepId, stepCard);
                            this.currentInferenceSection.appendChild(stepCard);
                        }
                        break;
                        
                    case 'inference_step_update':
                        if (data.stepId && data.updateContent) {
                            this.updateStepContent(data.stepId, data.updateContent, data.mode);
                        }
                        break;
                        
                    case 'inference_step_end':
                        if (data.stepId) {
                            this.finishStep(data.stepId, data.stepName, data.result, data.status || 'completed', data.referenceIds);
                        }
                        break;
                        
                    case 'answer_start':
                        this.currentAnswerDiv = this.addMessage('');
                        this.currentAnswer = '';
                        break;
                        
                    case 'answer_chunk':
                        if (this.currentAnswerDiv && data.text) {
                            this.currentAnswer += data.text;
                            this.currentAnswerDiv.textContent = this.currentAnswer;
                        }
                        break;
                        
                    case 'answer_references':
                        if (data.referenceIds && data.referenceIds.length > 0) {
                            const refsDiv = document.createElement('div');
                            refsDiv.style.cssText = 'margin-top: 10px; padding: 8px; background: #e8f4fd; border-radius: 4px;';
                            refsDiv.innerHTML = `<strong>引用:</strong> ${data.referenceIds.join(', ')}`;
                            this.currentAnswerDiv.parentNode.appendChild(refsDiv);
                        }
                        break;
                        
                    case 'error':
                        this.addMessage(`错误: ${data.message || '未知错误'}`);
                        break;
                }
            }
        }
        
        const testApp = new TestChatApp();
        
        function testConnection() {
            testApp.handleSSEEvent('connected', {
                message: '连接已建立',
                requestId: 'test-' + Date.now()
            });
        }
        
        function testInferenceFlow() {
            // 模拟完整的推理流程
            testApp.handleSSEEvent('inference_start', {});
            
            setTimeout(() => {
                testApp.handleSSEEvent('inference_step_start', {
                    stepId: 'step1',
                    stepName: '理解用户意图',
                    status: 'running'
                });
            }, 500);
            
            setTimeout(() => {
                testApp.handleSSEEvent('inference_step_update', {
                    stepId: 'step1',
                    updateContent: '正在分析用户问题...',
                    mode: 'append'
                });
            }, 1000);
            
            setTimeout(() => {
                testApp.handleSSEEvent('inference_step_update', {
                    stepId: 'step1',
                    updateContent: '识别关键词和上下文',
                    mode: 'newline'
                });
            }, 1500);
            
            setTimeout(() => {
                testApp.handleSSEEvent('inference_step_end', {
                    stepId: 'step1',
                    stepName: '理解用户意图',
                    result: '用户想要查询天气信息',
                    status: 'completed'
                });
            }, 2000);
            
            setTimeout(() => {
                testApp.handleSSEEvent('inference_step_start', {
                    stepId: 'step2',
                    stepName: '搜索相关信息',
                    status: 'running'
                });
            }, 2500);
            
            setTimeout(() => {
                testApp.handleSSEEvent('inference_step_end', {
                    stepId: 'step2',
                    stepName: '搜索相关信息',
                    result: '获取到北京天气数据',
                    status: 'completed',
                    referenceIds: ['weather_api_beijing']
                });
            }, 3000);
            
            setTimeout(() => {
                testApp.handleSSEEvent('inference_end', {});
            }, 3500);
        }
        
        function testAnswerFlow() {
            testApp.handleSSEEvent('answer_start', {});
            
            const answerText = '根据最新的天气数据，北京今天天气晴朗，气温25摄氏度，适合外出活动。';
            let index = 0;
            
            const interval = setInterval(() => {
                if (index < answerText.length) {
                    testApp.handleSSEEvent('answer_chunk', {
                        text: answerText[index],
                        index: index
                    });
                    index++;
                } else {
                    clearInterval(interval);
                    setTimeout(() => {
                        testApp.handleSSEEvent('answer_references', {
                            referenceIds: ['weather_api_beijing', 'location_service']
                        });
                    }, 500);
                    
                    setTimeout(() => {
                        testApp.handleSSEEvent('answer_end', {});
                    }, 1000);
                    
                    setTimeout(() => {
                        testApp.handleSSEEvent('stream_end', {});
                    }, 1500);
                }
            }, 50);
        }
        
        function testErrorEvent() {
            testApp.handleSSEEvent('error', {
                code: 'MODEL_ERROR',
                message: '模型处理超时，请稍后重试'
            });
        }
        
        function clearLog() {
            eventLog.innerHTML = '';
            chatContainer.innerHTML = '';
        }
    </script>
</body>
</html>
