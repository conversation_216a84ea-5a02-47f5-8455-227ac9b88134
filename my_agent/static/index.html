<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 问答助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: 600;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.assistant {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            word-wrap: break-word;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e1e5e9;
            border-bottom-left-radius: 5px;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            margin-bottom: 20px;
        }

        .typing-indicator .message-content {
            background: white;
            border: 1px solid #e1e5e9;
            padding: 15px 20px;
        }

        .typing-dots {
            display: inline-flex;
            align-items: center;
        }

        .typing-dots span {
            height: 8px;
            width: 8px;
            background: #999;
            border-radius: 50%;
            margin: 0 2px;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e1e5e9;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 15px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: translateY(-2px);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status-indicator {
            padding: 10px 20px;
            text-align: center;
            font-size: 14px;
            color: #666;
            background: #f0f0f0;
            border-top: 1px solid #e1e5e9;
        }

        .status-indicator.connected {
            background: #d4edda;
            color: #155724;
        }

        .status-indicator.error {
            background: #f8d7da;
            color: #721c24;
        }

        .inference-section {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            border-radius: 8px;
            font-size: 14px;
        }

        .inference-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .inference-title .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
            background: #28a745;
        }

        .inference-title .status-dot.running {
            background: #ffc107;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .step-card {
            margin: 10px 0;
            padding: 12px;
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .step-card.running {
            border-left: 4px solid #ffc107;
            background: #fffbf0;
        }

        .step-card.completed {
            border-left: 4px solid #28a745;
        }

        .step-card.failed {
            border-left: 4px solid #dc3545;
            background: #fff5f5;
        }

        .step-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .step-name {
            font-weight: 600;
            color: #333;
        }

        .step-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            text-transform: uppercase;
        }

        .step-status.running {
            background: #fff3cd;
            color: #856404;
        }

        .step-status.completed {
            background: #d4edda;
            color: #155724;
        }

        .step-status.failed {
            background: #f8d7da;
            color: #721c24;
        }

        .step-content {
            color: #666;
            line-height: 1.4;
            white-space: pre-wrap;
        }

        .step-result {
            margin-top: 8px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            font-style: italic;
            color: #495057;
        }

        .tool-call {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 8px;
            margin: 4px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .tool-call-name {
            font-weight: 600;
            color: #1976d2;
        }

        .references {
            margin-top: 15px;
            padding: 10px;
            background: #e8f4fd;
            border-radius: 6px;
            border-left: 4px solid #2196f3;
        }

        .references-title {
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 5px;
        }

        .reference-item {
            display: inline-block;
            margin: 2px 4px;
            padding: 2px 8px;
            background: #2196f3;
            color: white;
            border-radius: 12px;
            font-size: 12px;
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 90vh;
                border-radius: 10px;
            }
            
            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            AI 问答助手
        </div>
        
        <div class="status-indicator" id="statusIndicator">
            正在连接...
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-content">
                    您好！我是AI问答助手，有什么可以帮助您的吗？
                </div>
            </div>
        </div>
        
        <div class="typing-indicator" id="typingIndicator">
            <div class="message-content">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
        
        <div class="chat-input">
            <div class="input-group">
                <input type="text" class="message-input" id="messageInput" placeholder="请输入您的问题..." />
                <button class="send-button" id="sendButton">发送</button>
            </div>
        </div>
    </div>

    <script>
        class ChatApp {
            constructor() {
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
                this.chatMessages = document.getElementById('chatMessages');
                this.typingIndicator = document.getElementById('typingIndicator');
                this.statusIndicator = document.getElementById('statusIndicator');

                this.threadId = this.generateThreadId();
                this.isConnected = false;
                this.isProcessing = false;

                // 推理过程管理
                this.currentInferenceSection = null;
                this.inferenceSteps = new Map(); // stepId -> step element

                // 回答过程管理
                this.currentAnswerDiv = null;
                this.currentAnswer = '';
                this.answerReferences = [];

                this.init();
            }
            
            generateThreadId() {
                return 'thread_' + Math.random().toString(36).substr(2, 9);
            }
            
            init() {
                this.sendButton.addEventListener('click', () => this.sendMessage());
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
                
                this.updateStatus('已连接', 'connected');
                this.isConnected = true;
            }
            
            updateStatus(message, type = '') {
                this.statusIndicator.textContent = message;
                this.statusIndicator.className = 'status-indicator ' + type;
            }
            
            addMessage(content, isUser = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${isUser ? 'user' : 'assistant'}`;
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.textContent = content;
                
                messageDiv.appendChild(contentDiv);
                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();
                
                return contentDiv;
            }
            
            createInferenceSection() {
                const section = document.createElement('div');
                section.className = 'inference-section';
                section.innerHTML = `
                    <div class="inference-title">
                        <span class="status-dot running"></span>
                        推理过程
                    </div>
                `;
                this.chatMessages.appendChild(section);
                this.scrollToBottom();
                return section;
            }

            createStepCard(stepId, stepName, status = 'running') {
                const stepCard = document.createElement('div');
                stepCard.className = `step-card ${status}`;
                stepCard.setAttribute('data-step-id', stepId);

                stepCard.innerHTML = `
                    <div class="step-header">
                        <span class="step-name">${stepName}</span>
                        <span class="step-status ${status}">${status}</span>
                    </div>
                    <div class="step-content"></div>
                `;

                return stepCard;
            }

            updateStepContent(stepId, content, mode = 'append') {
                const stepCard = this.inferenceSteps.get(stepId);
                if (!stepCard) return;

                const contentDiv = stepCard.querySelector('.step-content');
                if (mode === 'newline') {
                    contentDiv.textContent += '\n' + content;
                } else {
                    contentDiv.textContent += content;
                }
                this.scrollToBottom();
            }

            finishStep(stepId, stepName, result, status, referenceIds = null) {
                const stepCard = this.inferenceSteps.get(stepId);
                if (!stepCard) return;

                // 更新状态
                stepCard.className = `step-card ${status}`;
                const statusSpan = stepCard.querySelector('.step-status');
                statusSpan.className = `step-status ${status}`;
                statusSpan.textContent = status;

                // 添加结果
                if (result) {
                    const resultDiv = document.createElement('div');
                    resultDiv.className = 'step-result';
                    resultDiv.textContent = `结果: ${result}`;
                    stepCard.appendChild(resultDiv);
                }

                // 添加引用
                if (referenceIds && referenceIds.length > 0) {
                    const refsDiv = document.createElement('div');
                    refsDiv.className = 'references';
                    refsDiv.innerHTML = `
                        <div class="references-title">引用:</div>
                        ${referenceIds.map(id => `<span class="reference-item">${id}</span>`).join('')}
                    `;
                    stepCard.appendChild(refsDiv);
                }

                this.scrollToBottom();
            }

            handleToolCall(stepId, toolCalls) {
                const stepCard = this.inferenceSteps.get(stepId);
                if (!stepCard) return;

                const contentDiv = stepCard.querySelector('.step-content');
                toolCalls.forEach(call => {
                    const toolDiv = document.createElement('div');
                    toolDiv.className = 'tool-call';
                    toolDiv.innerHTML = `
                        <div class="tool-call-name">调用工具: ${call.name}</div>
                        <div>参数: ${call.arguments}</div>
                    `;
                    contentDiv.appendChild(toolDiv);
                });
                this.scrollToBottom();
            }
            
            showTyping() {
                this.typingIndicator.style.display = 'flex';
                this.scrollToBottom();
            }
            
            hideTyping() {
                this.typingIndicator.style.display = 'none';
            }
            
            scrollToBottom() {
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }
            
            setProcessing(processing) {
                this.isProcessing = processing;
                this.sendButton.disabled = processing;
                this.messageInput.disabled = processing;
                
                if (processing) {
                    this.sendButton.textContent = '处理中...';
                    this.showTyping();
                } else {
                    this.sendButton.textContent = '发送';
                    this.hideTyping();
                }
            }
            
            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message || this.isProcessing) return;
                
                // 添加用户消息
                this.addMessage(message, true);
                this.messageInput.value = '';
                
                this.setProcessing(true);
                this.updateStatus('正在处理...', '');
                
                try {
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            messages: message,
                            thread_id: this.threadId
                        })
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();

                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n');

                        for (let i = 0; i < lines.length; i++) {
                            const line = lines[i];
                            if (line.startsWith('event: ') && lines[i + 1]?.startsWith('data: ')) {
                                const event = line.substring(7).trim();
                                const dataLine = lines[i + 1];
                                const data = dataLine.substring(6).trim();

                                try {
                                    let eventData = {};
                                    if (data && data !== '{}') {
                                        eventData = JSON.parse(data);
                                    }

                                    this.handleSSEEvent(event, eventData);
                                    i++; // 跳过data行
                                } catch (e) {
                                    console.error('解析事件数据失败:', e, 'Event:', event, 'Data:', data);
                                }
                            }
                        }
                    }
                    
                } catch (error) {
                    console.error('发送消息失败:', error);
                    this.updateStatus('连接错误', 'error');
                    this.addMessage('抱歉，无法连接到服务器。请检查网络连接后重试。');
                } finally {
                    this.setProcessing(false);
                    this.updateStatus('已连接', 'connected');
                    // 清理状态
                    this.currentInferenceSection = null;
                    this.currentAnswerDiv = null;
                    this.inferenceSteps.clear();
                }
            }
            
            handleSSEEvent(event, data) {
                console.log('SSE Event:', event, data);

                switch (event) {
                    case 'connected':
                        this.updateStatus(`已连接 (${data.requestId || ''})`, 'connected');
                        break;

                    case 'inference_start':
                        this.currentInferenceSection = this.createInferenceSection();
                        this.inferenceSteps.clear();
                        break;

                    case 'inference_step_start':
                        if (data.stepId && data.stepName) {
                            const stepCard = this.createStepCard(data.stepId, data.stepName, data.status || 'running');
                            this.inferenceSteps.set(data.stepId, stepCard);
                            this.currentInferenceSection.appendChild(stepCard);
                            this.scrollToBottom();
                        }
                        break;

                    case 'inference_step_update':
                        if (data.stepId && data.updateContent) {
                            if (Array.isArray(data.updateContent)) {
                                // 处理工具调用
                                this.handleToolCall(data.stepId, data.updateContent);
                            } else {
                                // 处理文本更新
                                this.updateStepContent(data.stepId, data.updateContent, data.mode);
                            }
                        }
                        break;

                    case 'inference_step_end':
                        if (data.stepId) {
                            this.finishStep(
                                data.stepId,
                                data.stepName,
                                data.result,
                                data.status || 'completed',
                                data.referenceIds
                            );
                        }
                        break;

                    case 'inference_end':
                        if (this.currentInferenceSection) {
                            const statusDot = this.currentInferenceSection.querySelector('.status-dot');
                            if (statusDot) {
                                statusDot.classList.remove('running');
                            }
                        }
                        break;

                    case 'answer_start':
                        this.hideTyping();
                        this.currentAnswerDiv = this.addMessage('');
                        this.currentAnswer = '';
                        this.answerReferences = [];
                        break;

                    case 'answer_chunk':
                        if (this.currentAnswerDiv && data.text) {
                            this.currentAnswer += data.text;
                            this.currentAnswerDiv.textContent = this.currentAnswer;
                            this.scrollToBottom();
                        }
                        break;

                    case 'answer_references':
                        if (data.referenceIds && data.referenceIds.length > 0) {
                            this.answerReferences = data.referenceIds;
                            this.displayAnswerReferences();
                        }
                        break;

                    case 'answer_more_link':
                        this.displayMoreLink(data);
                        break;

                    case 'answer_end':
                        // 答案结束，可以做一些清理工作
                        break;

                    case 'error':
                        this.updateStatus(`错误: ${data.message || '未知错误'}`, 'error');
                        this.addMessage(`抱歉，处理您的请求时发生了错误: ${data.message || '请稍后重试'}`);
                        break;

                    case 'stream_end':
                        this.updateStatus('已连接', 'connected');
                        break;

                    default:
                        console.log('未处理的事件类型:', event, data);
                }
            }

            displayAnswerReferences() {
                if (this.answerReferences.length === 0) return;

                const refsDiv = document.createElement('div');
                refsDiv.className = 'references';
                refsDiv.innerHTML = `
                    <div class="references-title">参考引用:</div>
                    ${this.answerReferences.map(id => `<span class="reference-item">${id}</span>`).join('')}
                `;

                // 将引用添加到当前答案后面
                if (this.currentAnswerDiv && this.currentAnswerDiv.parentNode) {
                    this.currentAnswerDiv.parentNode.appendChild(refsDiv);
                    this.scrollToBottom();
                }
            }

            displayMoreLink(data) {
                if (!data.url) return;

                const moreLinkDiv = document.createElement('div');
                moreLinkDiv.className = 'references';
                moreLinkDiv.innerHTML = `
                    <div class="references-title">获取更多信息:</div>
                    <button onclick="window.open('${data.url}', '_blank')" style="
                        background: #667eea;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        cursor: pointer;
                        margin-top: 5px;
                    ">查看更多</button>
                `;

                if (this.currentAnswerDiv && this.currentAnswerDiv.parentNode) {
                    this.currentAnswerDiv.parentNode.appendChild(moreLinkDiv);
                    this.scrollToBottom();
                }
            }
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new ChatApp();
        });
    </script>
</body>
</html>
