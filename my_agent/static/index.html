<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 问答助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: 600;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.assistant {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            word-wrap: break-word;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e1e5e9;
            border-bottom-left-radius: 5px;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            margin-bottom: 20px;
        }

        .typing-indicator .message-content {
            background: white;
            border: 1px solid #e1e5e9;
            padding: 15px 20px;
        }

        .typing-dots {
            display: inline-flex;
            align-items: center;
        }

        .typing-dots span {
            height: 8px;
            width: 8px;
            background: #999;
            border-radius: 50%;
            margin: 0 2px;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e1e5e9;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 15px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: translateY(-2px);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status-indicator {
            padding: 10px 20px;
            text-align: center;
            font-size: 14px;
            color: #666;
            background: #f0f0f0;
            border-top: 1px solid #e1e5e9;
        }

        .status-indicator.connected {
            background: #d4edda;
            color: #155724;
        }

        .status-indicator.error {
            background: #f8d7da;
            color: #721c24;
        }

        .inference-steps {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            border-radius: 5px;
            font-size: 14px;
            color: #666;
        }

        .step-item {
            margin: 5px 0;
            padding: 5px 0;
        }

        .step-item.active {
            color: #667eea;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 90vh;
                border-radius: 10px;
            }
            
            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            AI 问答助手
        </div>
        
        <div class="status-indicator" id="statusIndicator">
            正在连接...
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-content">
                    您好！我是AI问答助手，有什么可以帮助您的吗？
                </div>
            </div>
        </div>
        
        <div class="typing-indicator" id="typingIndicator">
            <div class="message-content">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
        
        <div class="chat-input">
            <div class="input-group">
                <input type="text" class="message-input" id="messageInput" placeholder="请输入您的问题..." />
                <button class="send-button" id="sendButton">发送</button>
            </div>
        </div>
    </div>

    <script>
        class ChatApp {
            constructor() {
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
                this.chatMessages = document.getElementById('chatMessages');
                this.typingIndicator = document.getElementById('typingIndicator');
                this.statusIndicator = document.getElementById('statusIndicator');
                
                this.threadId = this.generateThreadId();
                this.isConnected = false;
                this.isProcessing = false;
                this.currentInferenceSteps = null;
                
                this.init();
            }
            
            generateThreadId() {
                return 'thread_' + Math.random().toString(36).substr(2, 9);
            }
            
            init() {
                this.sendButton.addEventListener('click', () => this.sendMessage());
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
                
                this.updateStatus('已连接', 'connected');
                this.isConnected = true;
            }
            
            updateStatus(message, type = '') {
                this.statusIndicator.textContent = message;
                this.statusIndicator.className = 'status-indicator ' + type;
            }
            
            addMessage(content, isUser = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${isUser ? 'user' : 'assistant'}`;
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.textContent = content;
                
                messageDiv.appendChild(contentDiv);
                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();
                
                return contentDiv;
            }
            
            addInferenceSteps() {
                const stepsDiv = document.createElement('div');
                stepsDiv.className = 'inference-steps';
                stepsDiv.innerHTML = '<strong>推理过程：</strong>';
                this.chatMessages.appendChild(stepsDiv);
                this.scrollToBottom();
                return stepsDiv;
            }
            
            updateInferenceStep(stepsDiv, stepName, status = 'active') {
                const stepItem = document.createElement('div');
                stepItem.className = `step-item ${status}`;
                stepItem.textContent = `• ${stepName}`;
                stepsDiv.appendChild(stepItem);
                this.scrollToBottom();
            }
            
            showTyping() {
                this.typingIndicator.style.display = 'flex';
                this.scrollToBottom();
            }
            
            hideTyping() {
                this.typingIndicator.style.display = 'none';
            }
            
            scrollToBottom() {
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }
            
            setProcessing(processing) {
                this.isProcessing = processing;
                this.sendButton.disabled = processing;
                this.messageInput.disabled = processing;
                
                if (processing) {
                    this.sendButton.textContent = '处理中...';
                    this.showTyping();
                } else {
                    this.sendButton.textContent = '发送';
                    this.hideTyping();
                }
            }
            
            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message || this.isProcessing) return;
                
                // 添加用户消息
                this.addMessage(message, true);
                this.messageInput.value = '';
                
                this.setProcessing(true);
                this.updateStatus('正在处理...', '');
                
                try {
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            messages: message,
                            thread_id: this.threadId
                        })
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    
                    let assistantMessageDiv = null;
                    let currentAnswer = '';
                    
                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;
                        
                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n');
                        
                        for (const line of lines) {
                            if (line.startsWith('event: ') && lines[lines.indexOf(line) + 1]?.startsWith('data: ')) {
                                const event = line.substring(7);
                                const dataLine = lines[lines.indexOf(line) + 1];
                                const data = dataLine.substring(6);
                                
                                try {
                                    const eventData = JSON.parse(data);
                                    this.handleSSEEvent(event, eventData);
                                    
                                    // 处理不同类型的事件
                                    if (event === 'connected') {
                                        this.updateStatus('已连接', 'connected');
                                    } else if (event === 'inference_start') {
                                        this.currentInferenceSteps = this.addInferenceSteps();
                                    } else if (event === 'inference_step') {
                                        if (this.currentInferenceSteps && eventData.stepName) {
                                            this.updateInferenceStep(this.currentInferenceSteps, eventData.stepName);
                                        }
                                    } else if (event === 'answer_start') {
                                        this.hideTyping();
                                        assistantMessageDiv = this.addMessage('');
                                        currentAnswer = '';
                                    } else if (event === 'answer_chunk') {
                                        if (assistantMessageDiv && eventData.text) {
                                            currentAnswer += eventData.text;
                                            assistantMessageDiv.textContent = currentAnswer;
                                            this.scrollToBottom();
                                        }
                                    } else if (event === 'error') {
                                        this.updateStatus('发生错误: ' + eventData.message, 'error');
                                        this.addMessage('抱歉，处理您的请求时发生了错误。请稍后重试。');
                                    }
                                } catch (e) {
                                    console.error('解析事件数据失败:', e);
                                }
                            }
                        }
                    }
                    
                } catch (error) {
                    console.error('发送消息失败:', error);
                    this.updateStatus('连接错误', 'error');
                    this.addMessage('抱歉，无法连接到服务器。请检查网络连接后重试。');
                } finally {
                    this.setProcessing(false);
                    this.updateStatus('已连接', 'connected');
                    this.currentInferenceSteps = null;
                }
            }
            
            handleSSEEvent(event, data) {
                console.log('SSE Event:', event, data);
            }
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new ChatApp();
        });
    </script>
</body>
</html>
