#!/bin/bash

echo "========================================"
echo "AI 问答系统启动脚本"
echo "========================================"
echo

# 切换到脚本所在目录
cd "$(dirname "$0")"

echo "正在检查Python环境..."
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "错误: 未找到Python，请确保Python已安装"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "使用Python命令: $PYTHON_CMD"

echo "正在安装/更新依赖..."
$PYTHON_CMD -m pip install -r my_agent/requirements.txt

echo
echo "正在启动服务器..."
echo "服务启动后请访问: http://localhost:8000"
echo "按 Ctrl+C 停止服务"
echo "========================================"
echo

$PYTHON_CMD run_app.py
