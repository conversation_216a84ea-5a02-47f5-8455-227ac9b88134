#!/usr/bin/env python3
"""
MCP连接测试脚本
用于测试与MCP服务器的连接状态
"""

import asyncio
import logging
from my_agent.utils.mcp import client

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_mcp_connection(max_retries=3):
    """
    测试MCP服务器连接
    
    Args:
        max_retries (int): 最大重试次数
    
    Returns:
        bool: 连接是否成功
    """
    server_name = "ga"
    
    for attempt in range(max_retries):
        try:
            logger.info(f"尝试连接MCP服务器 (尝试 {attempt + 1}/{max_retries})")
            async with client.session(server_name) as session:
                # 尝试列出工具来测试连接
                tools_resp = await session.list_tools()
                logger.info(f"连接成功! 发现 {len(tools_resp.tools)} 个工具")
                
                # 显示前几个工具作为示例
                for i, tool in enumerate(tools_resp.tools[:5]):  # 只显示前5个
                    logger.info(f"  工具 {i+1}: {tool.name} - {tool.description}")
                
                if len(tools_resp.tools) > 5:
                    logger.info(f"  ... 还有 {len(tools_resp.tools) - 5} 个工具")
                
                return True
                
        except Exception as e:
            logger.warning(f"连接尝试 {attempt + 1} 失败: {e}")
            if attempt < max_retries - 1:
                wait_time = 2 ** attempt  # 指数退避
                logger.info(f"等待 {wait_time} 秒后重试...")
                await asyncio.sleep(wait_time)
            continue
    
    logger.error(f"无法连接到MCP服务器，已重试 {max_retries} 次")
    return False

async def main():
    """
    主函数
    """
    logger.info("开始MCP连接测试")
    
    success = await test_mcp_connection()
    
    if success:
        logger.info("MCP连接测试成功完成")
    else:
        logger.error("MCP连接测试失败")
        logger.info("请检查以下几点:")
        logger.info("1. MCP服务器地址是否正确 (当前配置: http://192.168.210.60:8091/sse)")
        logger.info("2. 网络连接是否正常")
        logger.info("3. MCP服务器是否正在运行")
        logger.info("4. 防火墙设置是否阻止了连接")

if __name__ == "__main__":
    asyncio.run(main())