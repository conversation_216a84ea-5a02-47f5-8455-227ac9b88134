# DeepSearch AI 问答系统

基于Python的智能问答系统，集成了现代化的Web前端界面和强大的AI推理能力。

## 功能特性

- 🎨 现代化的聊天界面设计
- 💬 实时流式对话体验
- 🔄 详细的推理过程可视化
- 📊 步骤生命周期管理（开始、更新、结束）
- 🔧 工具调用显示
- 📚 引用和参考链接支持
- 📱 响应式设计，支持移动端
- ⚡ 完整的SSE事件处理
- 🚨 错误处理和状态指示

## 快速开始

### 1. 安装依赖

确保已安装所有必要的Python包：

```bash
pip install -r my_agent/requirements.txt
```

### 2. 启动服务

有多种方式启动服务：

**方式一：使用项目根目录启动脚本（推荐）**
```bash
# Windows
start_server.bat

# Linux/Mac
./start_server.sh

# 或直接使用Python
python run_app.py
```

**方式二：进入my_agent目录启动**
```bash
cd my_agent
python run_server.py
```

**方式三：直接使用uvicorn**
```bash
cd my_agent
uvicorn apiServer:app --host 0.0.0.0 --port 8000 --reload
```

### 3. 访问应用

服务启动后，在浏览器中访问：
- **前端页面**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **测试页面**: http://localhost:8000/static/test.html
- **健康检查**: http://localhost:8000/health

## 界面说明

### 主要功能区域

1. **聊天头部**: 显示应用标题
2. **状态指示器**: 显示连接状态和处理状态
3. **消息区域**: 显示对话历史
4. **推理过程**: 显示AI的思考步骤
5. **输入区域**: 输入问题和发送按钮

### 交互方式

- 在输入框中输入问题
- 点击"发送"按钮或按Enter键发送
- 实时查看AI的推理过程：
  - 步骤开始时显示步骤名称和状态
  - 步骤执行过程中显示更新内容
  - 步骤完成时显示结果和引用
  - 工具调用时显示调用详情
- 流式接收AI回答
- 查看引用和参考链接
- 支持多轮对话

### 测试功能

访问 http://localhost:8000/static/test.html 可以：
- 测试各种SSE事件的处理
- 模拟完整的推理和回答流程
- 查看事件日志和界面效果
- 验证错误处理机制

## 技术架构

### 后端技术栈
- **FastAPI**: Web框架和API服务
- **LangGraph**: AI推理流程管理
- **LangChain**: 大模型集成
- **Server-Sent Events (SSE)**: 实时流式通信
- **Python**: 主要开发语言

### 前端技术栈
- **纯HTML/CSS/JavaScript**: 无框架依赖
- **SSE客户端**: 实时事件处理
- **响应式设计**: 适配各种设备

### 支持的SSE事件类型
根据《大模型问答 SSE 通信格式概要设计.md》实现：

**连接事件:**
- `connected`: 连接建立
- `stream_end`: 流结束

**推理过程事件:**
- `inference_start`: 推理开始
- `inference_step_start`: 步骤开始
- `inference_step_update`: 步骤更新（支持append和newline模式）
- `inference_step_end`: 步骤结束
- `inference_end`: 推理结束

**回答过程事件:**
- `answer_start`: 回答开始
- `answer_chunk`: 回答片段
- `answer_references`: 引用信息
- `answer_more_link`: 更多链接
- `answer_end`: 回答结束

**错误处理:**
- `error`: 错误信息

### API接口

主要API端点：
- `GET /`: 前端页面
- `POST /api/chat`: 聊天接口（SSE流式响应）
- `POST /api/createRequestId`: 创建请求ID
- `GET /api/mermaid`: 获取流程图
- `GET /health`: 健康检查
- `GET /static/*`: 静态文件服务

## 项目结构

```
deepsearch/
├── my_agent/                    # 主要应用目录
│   ├── static/                  # 前端静态文件
│   │   ├── index.html          # 主页面
│   │   └── test.html           # 测试页面
│   ├── utils/                   # 工具模块
│   │   ├── event_handler.py    # SSE事件处理
│   │   ├── nodes.py            # 推理节点
│   │   ├── tools.py            # 工具集成
│   │   └── ...
│   ├── agent.py                # AI代理逻辑
│   ├── apiServer.py            # FastAPI服务器
│   ├── requirements.txt        # Python依赖
│   └── run_server.py           # 启动脚本
├── run_app.py                  # 根目录启动脚本
├── start_server.bat            # Windows启动脚本
├── start_server.sh             # Linux/Mac启动脚本
├── test_routes.py              # 路由测试脚本
└── README.md                   # 项目文档
```

## 配置说明

### 修改端口
在相应的启动脚本中修改端口：

**run_app.py:**
```python
uvicorn.run(
    "apiServer:app",
    host="0.0.0.0",
    port=8080,  # 修改为你想要的端口
    reload=True,
    log_level="info"
)
```

### 修改样式
编辑 `my_agent/static/index.html` 中的CSS样式来自定义界面外观。

### 环境变量
可以通过环境变量配置：
- `PORT`: 服务端口（默认8000）
- `HOST`: 服务主机（默认0.0.0.0）

## 故障排除

### 常见问题

1. **端口被占用**
   - 修改端口号或停止占用端口的程序
   - 使用 `netstat -ano | findstr :8000` (Windows) 或 `lsof -i :8000` (Linux/Mac) 查看端口占用

2. **静态文件无法加载**
   - 确保 `my_agent/static` 目录存在且包含 `index.html`
   - 检查文件权限

3. **SSE连接失败**
   - 检查防火墙设置
   - 确保后端服务正常运行
   - 查看浏览器开发者工具的网络面板

4. **模块导入错误**
   - 确保从正确的目录启动服务
   - 检查Python路径配置

### 日志查看

服务运行时会在控制台显示详细日志，包括：
- 请求处理过程
- 推理步骤详情
- 错误信息和堆栈跟踪
- SSE事件发送记录

### 调试工具

1. **健康检查**: 访问 `/health` 端点查看服务状态
2. **测试页面**: 使用 `/static/test.html` 测试SSE事件处理
3. **API文档**: 访问 `/docs` 查看完整的API文档
4. **路由测试**: 运行 `python test_routes.py` 测试各个端点

## 开发指南

### 修改前端
主要文件：
- `my_agent/static/index.html`: 主页面和JavaScript逻辑
- `my_agent/static/test.html`: 测试页面

### 修改后端
主要文件：
- `my_agent/apiServer.py`: FastAPI服务器和路由
- `my_agent/agent.py`: AI代理逻辑
- `my_agent/utils/event_handler.py`: SSE事件处理
- `my_agent/utils/nodes.py`: 推理节点定义

### 添加新功能
1. 在相应的模块中添加功能代码
2. 更新API路由（如需要）
3. 更新前端界面（如需要）
4. 添加相应的测试

## 许可证

本项目采用开源许可证，具体许可证信息请查看项目根目录的LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 完整的SSE通信实现
- 现代化前端界面
- 详细的推理过程可视化